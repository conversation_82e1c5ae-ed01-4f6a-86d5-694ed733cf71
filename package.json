{"name": "archive_api", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "nodemon server.js", "build": "ncc build  -o build/lib ./server.js && pkg --target node18-win-x64 -o server.exe ./build/lib/index.js"}, "dependencies": {"axios": "^1.7.2", "bcryptjs": "^2.4.3", "bree": "^9.2.4", "bullmq": "^5.49.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.11.3", "nodemon": "^3.0.2", "sequelize": "^6.37.3", "serialport": "^12.0.0", "stimulsoft-reports-js": "^2024.3.6", "venom-bot": "^5.3.0"}, "author": "", "license": "ISC", "bin": {"execute": "server.js"}, "devDependencies": {"@vercel/ncc": "latest"}}
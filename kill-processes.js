#!/usr/bin/env node

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function killProcesses() {
    console.log("🔄 Killing processes that might be using COM ports...");
    console.log("====================================================\n");

    try {
        // List current Node.js processes
        console.log("📋 Current Node.js processes:");
        const { stdout: tasklistOutput } = await execAsync('tasklist /fi "imagename eq node.exe" /fo table');
        console.log(tasklistOutput);

        // Kill all Node.js processes except current one
        console.log("💀 Killing Node.js processes...");
        try {
            await execAsync('taskkill /f /im node.exe');
            console.log("✅ Node.js processes killed");
        } catch (error) {
            if (error.message.includes('not found')) {
                console.log("ℹ️  No Node.js processes to kill");
            } else {
                console.log("⚠️  Some processes might still be running");
            }
        }

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if any Node processes are still running
        try {
            const { stdout: checkOutput } = await execAsync('tasklist /fi "imagename eq node.exe" /fo table');
            if (checkOutput.includes('node.exe')) {
                console.log("⚠️  Some Node.js processes are still running:");
                console.log(checkOutput);
                console.log("\n💡 You may need to:");
                console.log("   - Close any open terminals");
                console.log("   - End processes manually in Task Manager");
                console.log("   - Restart your computer if needed");
            } else {
                console.log("✅ All Node.js processes have been stopped");
            }
        } catch (error) {
            console.log("✅ All Node.js processes have been stopped");
        }

        console.log("\n🔄 Now try running your worker again:");
        console.log("npm run worker");

    } catch (error) {
        console.error("❌ Error killing processes:", error.message);
        console.log("\n💡 Manual steps:");
        console.log("1. Open Task Manager (Ctrl+Shift+Esc)");
        console.log("2. Go to Details tab");
        console.log("3. Find and end all 'node.exe' processes");
        console.log("4. Try running your worker again");
    }
}

// Only run if this script is executed directly
if (require.main === module) {
    killProcesses().catch(console.error);
}

module.exports = { killProcesses };

const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");

let port = null;
let parser = null;
let isInitializing = false;

// Configuration - can be moved to environment variables
const MODEM_CONFIG = {
	path: process.env.MODEM_PORT || "COM6",
	baudRate: parseInt(process.env.MODEM_BAUD_RATE) || 9600,
	dataBits: 8,
	stopBits: 1,
	parity: "none",
	rtscts: false,
	xon: false,
	xoff: false,
	xany: false,
};

async function initializeClient(retryCount = 0) {
	const maxRetries = 3;

	if (port && port.isOpen) {
		console.log("📱 Port already open and ready");
		return port;
	}

	if (isInitializing) {
		console.log("⏳ Already initializing, waiting...");
		// Wait for current initialization to complete
		while (isInitializing) {
			await new Promise((resolve) => setTimeout(resolve, 100));
		}
		return port;
	}

	isInitializing = true;

	try {
		console.log(
			`📡 Initializing modem on ${MODEM_CONFIG.path} (attempt ${
				retryCount + 1
			}/${maxRetries + 1})`
		);

		// Close existing port if any
		if (port && !port.destroyed) {
			console.log("🔌 Closing existing port...");
			port.close();
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}

		port = new SerialPort({
			...MODEM_CONFIG,
			autoOpen: false,
		});

		parser = port.pipe(new ReadlineParser({ delimiter: "\r\n" }));

		// Set up error handlers
		port.on("error", (err) => {
			console.error("❌ Serial port error:", err.message);
		});

		port.on("close", () => {
			console.log("🔌 Serial port closed");
		});

		// Open port with promise
		await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error("Port open timeout"));
			}, 5000);

			port.open((err) => {
				clearTimeout(timeout);
				if (err) {
					console.error("❌ Failed to open port:", err.message);
					reject(err);
				} else {
					console.log("✅ Port opened successfully");
					resolve();
				}
			});
		});

		// Wait for port to be ready
		await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error("Port ready timeout"));
			}, 3000);

			port.once("open", () => {
				clearTimeout(timeout);
				resolve();
			});

			port.once("error", (err) => {
				clearTimeout(timeout);
				reject(err);
			});
		});

		// Test modem communication
		const isModemReady = await testModemCommunication();
		if (!isModemReady) {
			throw new Error("Modem not responding to AT commands");
		}

		console.log("🚀 Modem initialized successfully");
		return port;
	} catch (error) {
		console.error(
			`❌ Failed to initialize modem (attempt ${retryCount + 1}):`,
			error.message
		);

		if (retryCount < maxRetries) {
			console.log(`🔄 Retrying in 2 seconds...`);
			await new Promise((resolve) => setTimeout(resolve, 2000));
			return initializeClient(retryCount + 1);
		} else {
			throw new Error(
				`Failed to initialize modem after ${maxRetries + 1} attempts: ${
					error.message
				}`
			);
		}
	} finally {
		isInitializing = false;
	}
}

async function testModemCommunication() {
	return new Promise((resolve) => {
		let responseReceived = false;
		const responses = [];

		const timeout = setTimeout(() => {
			parser.off("data", onData);
			console.log(`📊 All responses received: ${JSON.stringify(responses)}`);
			// Consider it successful if we got any response
			resolve(responses.length > 0);
		}, 5000);

		const onData = (data) => {
			const cleanData = data.trim();
			if (cleanData) {
				console.log(`📥 Modem test response: "${cleanData}"`);
				responses.push(cleanData);

				if (cleanData.includes("OK")) {
					clearTimeout(timeout);
					parser.off("data", onData);
					responseReceived = true;
					resolve(true);
				}
			}
		};

		parser.on("data", onData);

		console.log("📤 Testing modem with AT command...");

		// First, try to cancel any ongoing operation
		port.write(String.fromCharCode(27)); // ESC

		setTimeout(() => {
			port.write("AT\r\n");
		}, 500);

		// Also try with just \r if no response
		setTimeout(() => {
			if (!responseReceived && responses.length === 0) {
				console.log("📤 Trying AT with \\r only...");
				port.write("AT\r");
			}
		}, 2500);
	});
}

async function sendMessage(to, message) {
	console.log(`📱 Attempting to send SMS to ${to}: "${message}"`);

	// Ensure modem is initialized
	if (!port || !port.isOpen) {
		console.log("🔄 Modem not ready, initializing...");
		await initializeClient();
	}

	// Validate inputs
	if (!to || !message) {
		throw new Error("Recipient and message are required");
	}

	return new Promise((resolve, reject) => {
		let state = "init";
		let buffer = [];
		let stepTimeout;

		// Add timeout for the entire operation
		const operationTimeout = setTimeout(() => {
			cleanup();
			reject(new Error("SMS operation timed out after 30 seconds"));
		}, 30000); // 30 seconds

		const onData = (data) => {
			const cleanData = data.trim();
			if (cleanData) {
				console.log(`📥 State: ${state}, Received: "${cleanData}"`);
				buffer.push(cleanData);

				// Reset step timeout on each response
				clearTimeout(stepTimeout);
				stepTimeout = setTimeout(() => {
					cleanup();
					reject(new Error(`Timeout waiting for response in state: ${state}`));
				}, 8000); // Increased to 8 seconds per step

				// Process responses
				if (
					cleanData === "OK" ||
					cleanData === "ERROR" ||
					cleanData === ">" ||
					cleanData.includes("CMGS:") ||
					cleanData.includes("+CMGS:")
				) {
					processBuffer();
				}
			}
		};

		const processBuffer = () => {
			console.log(`🔄 Processing buffer in state ${state}:`, buffer);

			switch (state) {
				case "init":
					// Look for OK response, or if we get echo + OK, or just move on if we get a prompt
					if (
						buffer.some((line) => line.includes("OK")) ||
						(buffer.includes("AT") && buffer.length >= 2)
					) {
						buffer = [];
						state = "setMode";
						console.log("📤 Sending AT+CMGF=1 (set text mode)");
						port.write("AT+CMGF=1\r\n");
					} else if (buffer.some((line) => line.includes("ERROR"))) {
						cleanup();
						reject(new Error("Modem did not respond OK to initial AT command"));
					} else if (buffer.includes(">")) {
						// Modem went directly to SMS mode, this shouldn't happen but let's handle it
						console.log("⚠️ Modem went directly to SMS prompt, canceling...");
						port.write(String.fromCharCode(27)); // ESC to cancel
						setTimeout(() => {
							buffer = [];
							state = "setMode";
							console.log("📤 Sending AT+CMGF=1 (set text mode)");
							port.write("AT+CMGF=1\r\n");
						}, 1000);
					}
					break;

				case "setMode":
					if (buffer.some((line) => line.includes("OK"))) {
						buffer = [];
						state = "sendNumber";
						console.log(`📤 Sending AT+CMGS="${to}" (set recipient)`);
						port.write(`AT+CMGS="${to}"\r\n`);
					} else if (buffer.some((line) => line.includes("ERROR"))) {
						cleanup();
						reject(new Error("Failed to set SMS text mode"));
					}
					break;

				case "sendNumber":
					if (buffer.some((line) => line.includes(">"))) {
						buffer = [];
						state = "sendMessage";
						console.log("📤 Sending message content + Ctrl+Z");
						port.write(message + String.fromCharCode(26)); // Ctrl+Z
					} else if (
						buffer.some((line) => line.includes("CMGS")) &&
						buffer.length >= 1
					) {
						// Got command echo, wait a bit then try sending message
						console.log("📥 Got command echo, trying to send message...");
						setTimeout(() => {
							if (state === "sendNumber") {
								buffer = [];
								state = "sendMessage";
								console.log("📤 Sending message content + Ctrl+Z (no prompt)");
								port.write(message + String.fromCharCode(26)); // Ctrl+Z
							}
						}, 1000);
					} else if (buffer.some((line) => line.includes("ERROR"))) {
						cleanup();
						reject(new Error("Failed to set recipient - no prompt received"));
					}
					break;

				case "sendMessage":
					// Look for OK or message reference or any numeric response
					if (
						buffer.some((line) => line.includes("OK")) ||
						buffer.some((line) => line.includes("CMGS:")) ||
						buffer.some((line) => line.includes("+CMGS:")) ||
						buffer.some((line) => /^\d+$/.test(line.trim())) || // Message reference number
						buffer.length > 0 // Accept any response after reasonable time
					) {
						cleanup();
						console.log("✅ SMS sent successfully");
						resolve("Message sent successfully");
					} else if (buffer.some((line) => line.includes("ERROR"))) {
						cleanup();
						reject(new Error("Failed to send SMS message"));
					}
					break;
			}
		};

		const cleanup = () => {
			clearTimeout(stepTimeout);
			clearTimeout(operationTimeout);
			if (parser) {
				parser.off("data", onData);
			}
		};

		// Set up data listener
		parser.on("data", onData);

		// Start the SMS sending process - first ensure we're in a clean state
		console.log("📤 Initializing SMS process...");

		// Send ESC first to cancel any ongoing operation
		port.write(String.fromCharCode(27)); // ESC

		// Wait a bit then start with AT
		setTimeout(() => {
			console.log("📤 Sending AT command");
			port.write("AT\r\n");
		}, 500);

		// Set initial step timeout
		stepTimeout = setTimeout(() => {
			cleanup();
			reject(
				new Error("No response to initial AT command - check modem connection")
			);
		}, 8000);
	});
}

module.exports = {
	initializeClient,
	sendMessage,
};

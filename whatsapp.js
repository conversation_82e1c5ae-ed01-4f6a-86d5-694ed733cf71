const serialportGsm = require("serialport-gsm");

let modem = null;
let isInitializing = false;

// Configuration - can be moved to environment variables
const MODEM_CONFIG = {
	path: process.env.MODEM_PORT || "COM6",
	baudRate: parseInt(process.env.MODEM_BAUD_RATE) || 9600,
	dataBits: 8,
	stopBits: 1,
	parity: "none",
	rtscts: false,
	xon: false,
	xoff: false,
	xany: false,
};

async function initializeClient(retryCount = 0) {
	const maxRetries = 3;

	if (modem && modem.isOpened) {
		console.log("📱 Modem already open and ready");
		return modem;
	}

	if (isInitializing) {
		console.log("⏳ Already initializing, waiting...");
		// Wait for current initialization to complete
		while (isInitializing) {
			await new Promise((resolve) => setTimeout(resolve, 100));
		}
		return modem;
	}

	isInitializing = true;

	try {
		console.log(
			`📡 Initializing GSM modem on ${MODEM_CONFIG.path} (attempt ${
				retryCount + 1
			}/${maxRetries + 1})`
		);

		// Close existing modem if any
		if (modem && modem.isOpened) {
			console.log("🔌 Closing existing modem...");
			await new Promise((resolve) => {
				modem.close(() => resolve());
			});
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}

		// Create new modem instance
		modem = serialportGsm.Modem();

		// Set up event handlers
		modem.on("error", (err) => {
			console.error("❌ GSM modem error:", err.message);
		});

		modem.on("close", () => {
			console.log("🔌 GSM modem closed");
		});

		// Open modem connection
		await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error("Modem open timeout"));
			}, 10000);

			modem.open(
				MODEM_CONFIG.path,
				{
					baudRate: MODEM_CONFIG.baudRate,
					dataBits: MODEM_CONFIG.dataBits,
					stopBits: MODEM_CONFIG.stopBits,
					parity: MODEM_CONFIG.parity,
				},
				(err) => {
					clearTimeout(timeout);
					if (err) {
						console.error("❌ Failed to open modem:", err.message);
						reject(err);
					} else {
						console.log("✅ GSM modem opened successfully");
						resolve();
					}
				}
			);
		});

		// Initialize modem
		await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error("Modem initialization timeout"));
			}, 5000);

			modem.initializeModem((err, result) => {
				clearTimeout(timeout);
				if (err) {
					console.error("❌ Failed to initialize modem:", err.message);
					reject(err);
				} else {
					console.log("✅ Modem initialized:", result);
					resolve();
				}
			});
		});

		console.log("🚀 GSM modem ready for SMS");
		return modem;
	} catch (error) {
		console.error(
			`❌ Failed to initialize GSM modem (attempt ${retryCount + 1}):`,
			error.message
		);

		if (retryCount < maxRetries) {
			console.log(`🔄 Retrying in 2 seconds...`);
			await new Promise((resolve) => setTimeout(resolve, 2000));
			return initializeClient(retryCount + 1);
		} else {
			throw new Error(
				`Failed to initialize GSM modem after ${maxRetries + 1} attempts: ${
					error.message
				}`
			);
		}
	} finally {
		isInitializing = false;
	}
}

async function sendMessage(to, message) {
	console.log(`📱 Attempting to send SMS to ${to}: "${message}"`);

	// Ensure modem is initialized
	if (!modem || !modem.isOpened) {
		console.log("🔄 Modem not ready, initializing...");
		await initializeClient();
	}

	// Validate inputs
	if (!to || !message) {
		throw new Error("Recipient and message are required");
	}

	return new Promise((resolve, reject) => {
		console.log("📤 Sending SMS using serialport-gsm...");

		// Use serialport-gsm's built-in SMS functionality
		modem.sendSMS(to, message, true, (err, result) => {
			if (err) {
				console.error("❌ Failed to send SMS:", err.message);
				reject(new Error(`Failed to send SMS: ${err.message}`));
			} else {
				console.log("✅ SMS sent successfully:", result);
				resolve("Message sent successfully");
			}
		});
	});
}

module.exports = {
	initializeClient,
	sendMessage,
};

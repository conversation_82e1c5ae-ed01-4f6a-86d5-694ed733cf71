const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");

let port = null;
let parser = null;

async function initializeClient() {
	if (port && port.isOpen) return port;

	port = new SerialPort({
		path: "COM8",
		baudRate: 115200,
		autoOpen: false,
	});

	parser = port.pipe(new ReadlineParser({ delimiter: "\r\n" }));

	port.open((err) => {
		if (err) {
			console.error("Failed to open port:", err.message);
		} else {
			console.log("Port opened");
		}
	});

	// Wait for port to open
	await new Promise((resolve, reject) => {
		port.on("open", resolve);
		port.on("error", reject);
	});

	// Optional: Listen for data from the modem for debugging
	parser.on("data", (data) => {
		console.log("Modem response:", data);
	});

	return port;
}

async function sendMessage(to, message) {
	if (!port || !port.isOpen) {
		await initializeClient();
	}

	return new Promise((resolve, reject) => {
		let state = "init";
		let buffer = [];
		let timeout;

		// Add timeout for the entire operation
		const operationTimeout = setTimeout(() => {
			cleanup();
			reject(new Error("SMS operation timed out"));
		}, 30000); // 30 seconds

		const onData = (data) => {
			console.log(`State: ${state}, Received: "${data}"`); // Debug log
			buffer.push(data);

			// Reset timeout on each response
			clearTimeout(timeout);
			timeout = setTimeout(() => {
				cleanup();
				reject(new Error(`Timeout in state: ${state}`));
			}, 5000);

			if (data === "OK" || data === "ERROR" || data === ">") {
				processBuffer();
			}
		};

		const processBuffer = () => {
			console.log(`Processing buffer in state ${state}:`, buffer); // Debug log

			switch (state) {
				case "init":
					if (buffer.includes("OK")) {
						buffer = [];
						state = "setMode";
						console.log("Sending AT+CMGF=1");
						port.write("AT+CMGF=1\r");
					} else if (buffer.includes("ERROR")) {
						cleanup();
						reject(new Error("Modem did not respond OK to initial AT."));
					}
					break;

				case "setMode":
					if (buffer.includes("OK")) {
						buffer = [];
						state = "sendNumber";
						console.log(`Sending AT+CMGS="${to}"`);
						port.write(`AT+CMGS="${to}"\r`);
					} else if (buffer.includes("ERROR")) {
						cleanup();
						reject(new Error("Failed to set text mode."));
					}
					break;

				case "sendNumber":
					if (buffer.includes(">")) {
						buffer = [];
						state = "sendMessage";
						console.log("Sending message content");
						port.write(message + String.fromCharCode(26)); // Ctrl+Z
					} else if (buffer.includes("ERROR")) {
						cleanup();
						reject(new Error("No prompt for message input."));
					}
					break;

				case "sendMessage":
					if (buffer.includes("OK")) {
						cleanup();
						resolve("Message sent successfully");
					} else if (buffer.includes("ERROR")) {
						cleanup();
						reject(new Error("Failed to send message."));
					}
					break;
			}
		};

		const cleanup = () => {
			clearTimeout(timeout);
			clearTimeout(operationTimeout);
			parser.off("data", onData);
		};

		parser.on("data", onData);

		// Start with a simple AT command
		console.log("Sending initial AT command");
		port.write("AT\r");

		// Set initial timeout
		timeout = setTimeout(() => {
			cleanup();
			reject(new Error("No response to initial AT command"));
		}, 5000);
	});
}

module.exports = {
	initializeClient,
	sendMessage,
};

# SMS Modem Troubleshooting Guide

## Current Issue: "Timeout in state: init"

This error means the modem is not responding to the initial AT command. Here's how to diagnose and fix it:

## Step 1: Run Modem Diagnostics

```bash
npm run test:modem
```

This will:
- List all available serial ports
- Test communication with the modem on COM8
- Try different line endings and commands

## Step 2: Check Physical Connection

1. **Verify USB/Serial Connection**
   - Ensure the modem is properly connected
   - Check if the USB cable is working
   - Try a different USB port

2. **Check Device Manager (Windows)**
   - Open Device Manager
   - Look under "Ports (COM & LPT)"
   - Verify COM8 is listed and working
   - Note the actual COM port number

## Step 3: Verify COM Port

If the modem is on a different COM port:

1. Update `config.env`:
   ```
   MODEM_PORT=COM3
   ```
   (Replace COM3 with your actual port)

2. Or run the diagnostic to see available ports:
   ```bash
   npm run test:modem
   ```

## Step 4: Try Different Baud Rates

Common baud rates for GSM modems:
- 9600
- 38400
- 57600
- 115200 (current)

Update `config.env`:
```
MODEM_BAUD_RATE=9600
```

## Step 5: Test Modem Manually

Use a terminal program like PuTTY or Windows Terminal:

1. Connect to COM8 at 115200 baud
2. Type: `AT` and press Enter
3. You should see: `OK`

If this doesn't work, the issue is hardware/driver related.

## Step 6: Check Modem Status

Some modems need time to initialize. Try:

1. **Power cycle the modem**
   - Unplug and reconnect
   - Wait 30 seconds before testing

2. **Check SIM card**
   - Ensure SIM is properly inserted
   - SIM should have credit/active plan

## Step 7: Driver Issues

If the modem isn't recognized:

1. **Install modem drivers**
   - Check manufacturer's website
   - Install specific drivers for your modem model

2. **Try different USB drivers**
   - Some modems need specific USB-to-serial drivers

## Step 8: Alternative Ports

Try testing other COM ports that might be your modem:

```bash
# Test COM3
MODEM_PORT=COM3 npm run test:modem

# Test COM4  
MODEM_PORT=COM4 npm run test:modem
```

## Step 9: Modem-Specific Commands

Some modems require specific initialization:

1. **Check modem model**
   - Look for model number on device
   - Search for specific AT commands

2. **Common alternatives to try:**
   - `ATZ` (reset modem)
   - `ATE0` (disable echo)
   - `AT+CFUN=1` (set full functionality)

## Step 10: Clear Queue and Restart

```bash
# Clear any stuck jobs
npm run queue:clear

# Start fresh
npm run worker
```

## Common Solutions

### Solution 1: Wrong COM Port
- Most common issue
- Check Device Manager for correct port
- Update `MODEM_PORT` in config.env

### Solution 2: Baud Rate Mismatch
- Try 9600 baud rate first
- Many older modems use 9600

### Solution 3: Driver Issues
- Install manufacturer drivers
- Restart computer after driver installation

### Solution 4: Hardware Problem
- Try different USB cable
- Try different USB port
- Test modem on different computer

## Testing Commands

```bash
# Test modem connection
npm run test:modem

# Check queue status
npm run queue:status

# Clear stuck jobs
npm run queue:clear

# Start worker with debug info
npm run worker:dev
```

## Success Indicators

When working correctly, you should see:
```
✅ Port opened successfully
📥 Modem test response: "OK"
🚀 Modem initialized successfully
```

## Still Not Working?

1. **Check modem compatibility**
   - Ensure it supports AT commands
   - Verify it's a GSM/SMS modem

2. **Try different software**
   - Test with manufacturer's software first
   - Confirm modem works outside of this application

3. **Hardware replacement**
   - Consider trying a different modem
   - Some modems are not compatible with Node.js SerialPort

## Environment Variables

Add to `config.env`:
```
MODEM_PORT=COM8          # Your modem's COM port
MODEM_BAUD_RATE=115200   # Baud rate (try 9600 if issues)
```

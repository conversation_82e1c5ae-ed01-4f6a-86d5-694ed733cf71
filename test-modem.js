#!/usr/bin/env node

const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");

async function listPorts() {
    console.log("📋 Available Serial Ports:");
    console.log("========================");
    
    try {
        const ports = await SerialPort.list();
        if (ports.length === 0) {
            console.log("❌ No serial ports found");
            return;
        }
        
        ports.forEach((port, index) => {
            console.log(`${index + 1}. ${port.path}`);
            console.log(`   Manufacturer: ${port.manufacturer || 'Unknown'}`);
            console.log(`   Product ID: ${port.productId || 'Unknown'}`);
            console.log(`   Vendor ID: ${port.vendorId || 'Unknown'}`);
            console.log(`   Serial Number: ${port.serialNumber || 'Unknown'}`);
            console.log("");
        });
    } catch (error) {
        console.error("❌ Error listing ports:", error);
    }
}

async function testModem(portPath = "COM8", baudRate = 115200) {
    console.log(`🔍 Testing modem on ${portPath} at ${baudRate} baud...`);
    console.log("================================================");
    
    let port = null;
    let parser = null;
    
    try {
        // Create port
        port = new SerialPort({
            path: portPath,
            baudRate: baudRate,
            autoOpen: false,
            // Add additional settings that might help
            dataBits: 8,
            stopBits: 1,
            parity: 'none',
            rtscts: false,
            xon: false,
            xoff: false,
            xany: false,
        });
        
        parser = port.pipe(new ReadlineParser({ delimiter: "\r\n" }));
        
        // Open port
        await new Promise((resolve, reject) => {
            port.open((err) => {
                if (err) {
                    console.error("❌ Failed to open port:", err.message);
                    reject(err);
                } else {
                    console.log("✅ Port opened successfully");
                    resolve();
                }
            });
        });
        
        // Wait for port to be ready
        await new Promise((resolve, reject) => {
            port.on("open", resolve);
            port.on("error", reject);
        });
        
        console.log("📡 Port is ready, starting modem test...");
        
        // Set up data listener
        const responses = [];
        parser.on("data", (data) => {
            console.log(`📥 Received: "${data}"`);
            responses.push(data);
        });
        
        // Test basic AT command
        console.log("📤 Sending: AT");
        port.write("AT\r\n");
        
        // Wait for response
        await new Promise((resolve) => {
            setTimeout(resolve, 2000);
        });
        
        if (responses.length === 0) {
            console.log("❌ No response to AT command");
            
            // Try different line endings
            console.log("📤 Trying with \\r only...");
            port.write("AT\r");
            await new Promise((resolve) => setTimeout(resolve, 2000));
            
            if (responses.length === 0) {
                console.log("❌ Still no response");
                
                // Try with different baud rates
                console.log("💡 Suggestion: Try different baud rates (9600, 38400, 57600, 115200)");
                console.log("💡 Suggestion: Check if modem is properly connected");
                console.log("💡 Suggestion: Try different COM ports");
            }
        } else {
            console.log("✅ Modem is responding!");
            
            // Test SMS mode
            console.log("📤 Testing SMS text mode: AT+CMGF=1");
            port.write("AT+CMGF=1\r\n");
            
            await new Promise((resolve) => setTimeout(resolve, 2000));
            
            // Test signal strength
            console.log("📤 Testing signal strength: AT+CSQ");
            port.write("AT+CSQ\r\n");
            
            await new Promise((resolve) => setTimeout(resolve, 2000));
            
            // Test SIM card
            console.log("📤 Testing SIM card: AT+CPIN?");
            port.write("AT+CPIN?\r\n");
            
            await new Promise((resolve) => setTimeout(resolve, 2000));
        }
        
    } catch (error) {
        console.error("❌ Error during modem test:", error);
    } finally {
        if (port && port.isOpen) {
            console.log("🔌 Closing port...");
            port.close();
        }
    }
}

async function main() {
    console.log("🔧 SMS Modem Diagnostic Tool");
    console.log("============================\n");
    
    await listPorts();
    console.log("");
    await testModem();
    
    console.log("\n🏁 Diagnostic complete");
    process.exit(0);
}

main().catch(console.error);

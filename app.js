const express = require("express");
const globalErrorHandler = require("./controllers/errorController");
const notificationsRouter = require("./routes/notificationsRoute");

const cors = require("cors");
const AppError = require("./utils/appError");
const corsOptions = {
	origin: "*",
	// "Access-Control-Allow-Credentials": true,
	// optionSuccessStatus: 200,
	credentials: false,
};

// Get a list of available devices
const app = express();
app.use((req, res, next) => {
	res.header("Access-Control-Allow-Credentials", true);
	next();
});
app.use(cors(corsOptions));
app.use(express.json());

app.use("/api/v1/notifications", notificationsRouter);

app.all("/*", (req, res, next) => {
	next(new AppError(`cant find ${req.originalUrl} on this server`, 404));
});
app.use(globalErrorHandler);
module.exports = app;

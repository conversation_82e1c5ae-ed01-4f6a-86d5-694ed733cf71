#!/usr/bin/env node

const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });

const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");

async function simpleTest() {
    const portPath = process.env.MODEM_PORT || "COM8";
    const baudRate = parseInt(process.env.MODEM_BAUD_RATE) || 115200;
    
    console.log(`🧪 Simple Modem Test on ${portPath} at ${baudRate} baud`);
    console.log("=".repeat(50));
    
    let port = null;
    let parser = null;
    
    try {
        port = new SerialPort({
            path: portPath,
            baudRate: baudRate,
            dataBits: 8,
            stopBits: 1,
            parity: 'none',
            autoOpen: false,
        });

        parser = port.pipe(new ReadlineParser({ delimiter: "\r\n" }));

        // Open port
        await new Promise((resolve, reject) => {
            port.open((err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log("✅ Port opened");
                    resolve();
                }
            });
        });

        // Wait for port to be ready
        await new Promise((resolve) => {
            port.once("open", resolve);
        });

        console.log("📡 Port ready, starting test sequence...\n");

        // Collect all responses
        const allResponses = [];
        parser.on("data", (data) => {
            const cleanData = data.trim();
            if (cleanData) {
                console.log(`📥 Raw response: "${cleanData}"`);
                allResponses.push(cleanData);
            }
        });

        // Test sequence
        console.log("1️⃣ Sending ESC to cancel any ongoing operation...");
        port.write(String.fromCharCode(27)); // ESC
        await wait(1000);

        console.log("2️⃣ Sending AT command...");
        port.write("AT\r\n");
        await wait(2000);

        console.log("3️⃣ Sending ATE0 to disable echo...");
        port.write("ATE0\r\n");
        await wait(2000);

        console.log("4️⃣ Sending AT again to test...");
        port.write("AT\r\n");
        await wait(2000);

        console.log("5️⃣ Testing SMS text mode...");
        port.write("AT+CMGF=1\r\n");
        await wait(2000);

        console.log("6️⃣ Testing signal strength...");
        port.write("AT+CSQ\r\n");
        await wait(2000);

        console.log("\n📊 Test Summary:");
        console.log("================");
        console.log(`Total responses: ${allResponses.length}`);
        
        if (allResponses.length === 0) {
            console.log("❌ No responses received - modem not communicating");
        } else {
            console.log("✅ Modem is responding");
            console.log("All responses:", allResponses);
            
            // Analyze responses
            const hasOK = allResponses.some(r => r.includes("OK"));
            const hasError = allResponses.some(r => r.includes("ERROR"));
            const hasEcho = allResponses.some(r => r.startsWith("AT"));
            const hasPrompt = allResponses.some(r => r.includes(">"));
            
            console.log("\n🔍 Analysis:");
            console.log(`- Has OK responses: ${hasOK ? '✅' : '❌'}`);
            console.log(`- Has ERROR responses: ${hasError ? '⚠️' : '✅'}`);
            console.log(`- Has command echo: ${hasEcho ? '⚠️ (should disable)' : '✅'}`);
            console.log(`- Has SMS prompt (>): ${hasPrompt ? '⚠️ (unexpected)' : '✅'}`);
            
            if (hasEcho) {
                console.log("\n💡 Recommendation: Modem has echo enabled");
                console.log("   - This is normal but can cause confusion");
                console.log("   - ATE0 command should disable it");
            }
            
            if (hasPrompt && !allResponses.some(r => r.includes("CMGS"))) {
                console.log("\n⚠️  Warning: Unexpected SMS prompt");
                console.log("   - Modem might be in SMS mode");
                console.log("   - Send ESC to cancel before AT commands");
            }
        }

    } catch (error) {
        console.error("❌ Test failed:", error.message);
    } finally {
        if (port && port.isOpen) {
            console.log("\n🔌 Closing port...");
            port.close();
        }
        process.exit(0);
    }
}

function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

simpleTest().catch(console.error);

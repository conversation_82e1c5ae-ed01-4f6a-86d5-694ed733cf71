// queues/notificationQueue.js
const { Queue } = require("bullmq");

const notificationQueue = new Queue("notification", {
	connection: {
		host: "127.0.0.1",
		port: 6379,
		maxRetriesPerRequest: null,
	},
	defaultJobOptions: {
		// Set job timeout to be longer than SMS operation timeout
		timeout: 45000, // 45 seconds (longer than 30s SMS timeout)
		attempts: 3, // Retry failed jobs up to 3 times
		backoff: {
			type: "exponential",
			delay: 5000,
		},
		removeOnComplete: 10, // Keep only last 10 completed jobs
		removeOnFail: 50, // Keep last 50 failed jobs for debugging
	},
});

module.exports = notificationQueue;

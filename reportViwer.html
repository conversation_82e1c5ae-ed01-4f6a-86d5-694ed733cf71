<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>Hello Stimulsoft Viewer</title>
	</head>
	<body>
		<div id="viewer"></div>

		<script src="stimulsoft/stimulsoft.reports.js"></script>
		<script src="stimulsoft/stimulsoft.viewer.js"></script>
		<script>
			document.addEventListener("DOMContentLoaded", function () {
				var viewer = new Stimulsoft.Viewer.StiViewer(null, "StiViewer", false);
				Stimulsoft.Base.StiLicense.loadFromFile("stimulsoft/license.key");

				var report = new Stimulsoft.Report.StiReport();
				report.loadFile("reportsTemplates/Report.mrt");

				var dataSet = new Stimulsoft.System.Data.DataSet("Demo");
				dataSet.readJsonFile("stimulsoft/Demo.json");

				report.dictionary.databases.clear();
				report.regData("Demo", "Demo", dataSet);

				viewer.report = report;
				viewer.renderHtml("viewer");
			});
		</script>
	</body>
</html>

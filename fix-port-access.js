#!/usr/bin/env node

const { SerialPort } = require("serialport");
const { exec } = require("child_process");
const { promisify } = require("util");

const execAsync = promisify(exec);

async function checkPortAccess() {
	console.log("🔍 Diagnosing COM Port Access Issues");
	console.log("===================================\n");

	// 1. List all available ports
	console.log("📋 Step 1: Listing all available serial ports...");
	try {
		const ports = await SerialPort.list();
		if (ports.length === 0) {
			console.log("❌ No serial ports found");
			return;
		}

		console.log("Available ports:");
		ports.forEach((port, index) => {
			console.log(`${index + 1}. ${port.path}`);
			console.log(`   Manufacturer: ${port.manufacturer || "Unknown"}`);
			console.log(`   Product ID: ${port.productId || "Unknown"}`);
			console.log(`   Vendor ID: ${port.vendorId || "Unknown"}`);
			if (port.path === "COM6") {
				console.log(`   ⚠️  This is your configured port (COM6)`);
			}
			console.log("");
		});
	} catch (error) {
		console.error("❌ Error listing ports:", error.message);
		return;
	}

	// 2. Check what's using COM6
	console.log("🔍 Step 2: Checking what might be using COM6...");
	try {
		// Check for processes that might be using serial ports
		const { stdout } = await execAsync(
			'tasklist /fi "imagename eq node.exe" /fo csv'
		);
		const nodeProcesses = stdout
			.split("\n")
			.filter((line) => line.includes("node.exe"));

		if (nodeProcesses.length > 1) {
			console.log("⚠️  Multiple Node.js processes found:");
			console.log("This might indicate another instance is using the port");
			console.log("Node processes:", nodeProcesses.length - 1); // -1 for header
		}
	} catch (error) {
		console.log("ℹ️  Could not check running processes");
	}

	// 3. Try to identify the correct port
	console.log("🔍 Step 3: Testing each available port...");
	const ports = await SerialPort.list();

	for (const portInfo of ports) {
		if (portInfo.path.startsWith("COM")) {
			console.log(`\n🧪 Testing ${portInfo.path}...`);
			await testSinglePort(portInfo.path);
		}
	}

	// 4. Provide solutions
	console.log("\n💡 Solutions to try:");
	console.log("===================");
	console.log("1. Kill any running Node.js processes:");
	console.log("   - Close all terminals running your app");
	console.log("   - Check Task Manager for node.exe processes");
	console.log("   - Run: taskkill /f /im node.exe");
	console.log("");
	console.log("2. Check if another app is using the modem:");
	console.log("   - Close any SMS software");
	console.log("   - Close any modem management tools");
	console.log("   - Disconnect and reconnect the modem");
	console.log("");
	console.log("3. Run as Administrator:");
	console.log("   - Right-click Command Prompt -> Run as Administrator");
	console.log("   - Then run your npm commands");
	console.log("");
	console.log("4. Try a different COM port:");
	console.log("   - Update MODEM_PORT in config.env");
	console.log("   - Use one of the ports listed above");
}

async function testSinglePort(portPath) {
	try {
		const port = new SerialPort({
			path: portPath,
			baudRate: 9600, // Start with common baud rate
			autoOpen: false,
		});

		await new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error("Timeout"));
			}, 3000);

			port.open((err) => {
				clearTimeout(timeout);
				if (err) {
					if (err.message.includes("Access denied")) {
						console.log(`   ❌ Access denied - port in use`);
					} else if (err.message.includes("cannot open")) {
						console.log(`   ❌ Cannot open - ${err.message}`);
					} else {
						console.log(`   ❌ Error: ${err.message}`);
					}
					reject(err);
				} else {
					console.log(`   ✅ Port opened successfully`);
					port.close();
					resolve();
				}
			});
		});
	} catch (error) {
		// Error already logged above
	}
}

checkPortAccess().catch(console.error);

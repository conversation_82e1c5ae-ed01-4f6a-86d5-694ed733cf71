#!/usr/bin/env node

const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });

const notificationQueue = require("./queues/notificationQueue");

async function checkQueueStatus() {
    try {
        console.log("📊 Queue Status Report");
        console.log("=====================");
        
        const waiting = await notificationQueue.getWaiting();
        const active = await notificationQueue.getActive();
        const completed = await notificationQueue.getCompleted();
        const failed = await notificationQueue.getFailed();
        const delayed = await notificationQueue.getDelayed();
        
        console.log(`⏳ Waiting jobs: ${waiting.length}`);
        console.log(`🔄 Active jobs: ${active.length}`);
        console.log(`✅ Completed jobs: ${completed.length}`);
        console.log(`❌ Failed jobs: ${failed.length}`);
        console.log(`⏰ Delayed jobs: ${delayed.length}`);
        
        if (active.length > 0) {
            console.log("\n🔄 Active Jobs:");
            active.forEach(job => {
                console.log(`  - Job ${job.id}: ${job.name} (${job.data.recipient})`);
            });
        }
        
        if (failed.length > 0) {
            console.log("\n❌ Recent Failed Jobs:");
            failed.slice(-5).forEach(job => {
                console.log(`  - Job ${job.id}: ${job.failedReason}`);
            });
        }
        
        console.log("\n=====================");
        
    } catch (error) {
        console.error("❌ Error checking queue status:", error);
    } finally {
        await notificationQueue.close();
        process.exit(0);
    }
}

checkQueueStatus();

const { Worker } = require("bullmq");
const { sendMessage, sendImage, initializeClient } = require("../whatsapp");
const { Buffer } = require("buffer");
const path = require("path");

(async () => {
	await initializeClient(); // تأكد من التهيئة أولاً

	const worker = new Worker(
		"notification",
		async (job) => {
			console.log(`🔄 Processing job ${job.id} of type: ${job.name}`);

			// Only process send-whatsapp jobs
			if (job.name !== "send-whatsapp") {
				console.log(
					`⚠️ Skipping job ${job.id} - unknown job type: ${job.name}`
				);
				return;
			}

			const { recipient, message } = job.data;
			const formattedNumber = `${recipient}`;

			try {
				console.log(`📱 Sending SMS to ${formattedNumber}`);
				await sendMessage(formattedNumber, message);
				console.log(`✅ SMS sent successfully to ${formattedNumber}`);
			} catch (err) {
				console.error(`❌ Failed to send message to ${formattedNumber}:`, err);
				throw err; // Will retry according to backoff settings
			}
		},
		{
			connection: {
				host: "127.0.0.1",
				port: 6379,
			},
			// Configure stall settings for long-running SMS operations
			stalledInterval: 60 * 1000, // 60 seconds - longer than SMS timeout
			maxStalledCount: 3, // Allow 3 stalls before failing
			backoff: {
				type: "exponential",
				delay: 5000,
			},
			// Set concurrency to 1 to avoid serial port conflicts
			concurrency: 1,
		}
	);

	worker.on("completed", (job) => {
		console.log(`✅ Job ${job.id} completed successfully`);
	});

	worker.on("failed", (job, err) => {
		console.error(`❌ Job ${job.id} failed:`, err);
	});

	worker.on("stalled", (jobId) => {
		console.warn(`⚠️ Job ${jobId} stalled - will be retried`);
	});

	console.log("🚀 SMS Worker started successfully");
})();

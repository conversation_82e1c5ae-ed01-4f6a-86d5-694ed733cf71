const { Worker } = require("bullmq");
const { sendMessage, sendImage, initializeClient } = require("../whatsapp");
const { Buffer } = require("buffer");
const path = require("path");

(async () => {
	await initializeClient(); // تأكد من التهيئة أولاً

	const worker = new Worker(
		"notification",
		async (job) => {
			const { recipient, message } = job.data;
			const formattedNumber = `${recipient}`;

			try {
				await sendMessage(formattedNumber, message);
			} catch (err) {
				console.error(`❌ Failed to send message:`, err);
				throw err; // Will retry according to backoff settings
			}
		},
		{
			connection: {
				host: "127.0.0.1",
				port: 6379,
			},
			backoff: {
				type: "exponential",
				delay: 5000,
			},
		}
	);

	worker.on("completed", (job) => {});

	worker.on("failed", (job, err) => {
		console.error(`❌ Job ${job.id} failed:`, err);
	});
})();

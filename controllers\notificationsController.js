const catchAsync = require("../utils/catchAsync");
const AppError = require("../utils/appError");
const notificationQueue = require("../queues/notificationQueue");

exports.sendNotification = catchAsync(async (req, res, next) => {
	try {
		await notificationQueue.add("send-whatsapp", {
			recipient: "+967777042721",
			message: `testyyyyyyy`,
		});
		res.status(200).json({ state: "success" });
	} catch (error) {
		return next(new AppError(error, 500));
	}
});

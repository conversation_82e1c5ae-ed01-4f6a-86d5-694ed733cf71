const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });
const app = require("./app");
const port = process.env.PORT;
const fs = require("fs");
app.listen(port, () => {
	console.log(`🚀 Server started successfully on port ${port}!!`);
});

const { Worker } = require("bullmq");
const {
	sendMessage,
	initializeClient,
	sendImage,
	sendFile,
} = require("./whatsapp");

(async () => {
	try {
		await initializeClient();
		const worker = new Worker(
			"notification",
			async (job) => {
				const { recipient, message } = job.data;
				const formattedNumber = `${recipient}`;
				try {
					await sendMessage(formattedNumber, message);
				} catch (err) {
					throw err; // BullMQ سيعيد المحاولة تلقائيًا
				}
			},
			{
				connection: {
					host: "127.0.0.1",
					port: 6379,
				},
				backoff: {
					type: "exponential",
					delay: 5000,
				},
			}
		);
		worker.on("failed", (job, err) => {
			console.error(`❌ Job ${job.id} failed:`, err);
		});
	} catch (err) {
		console.error("❌ Error initializing  or worker:", err);
	}
})();

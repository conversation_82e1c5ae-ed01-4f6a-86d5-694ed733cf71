#!/usr/bin/env node

const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });

const notificationQueue = require("./queues/notificationQueue");

async function clearStalledJobs() {
    try {
        console.log("🧹 Clearing stalled jobs...");
        
        // Get all active jobs (these might be stalled)
        const active = await notificationQueue.getActive();
        console.log(`Found ${active.length} active jobs`);
        
        // Clean stalled jobs
        const stalledJobs = await notificationQueue.clean(0, 0, 'stalled');
        console.log(`✅ Cleaned ${stalledJobs.length} stalled jobs`);
        
        // Also clean old completed and failed jobs
        const oldCompleted = await notificationQueue.clean(24 * 60 * 60 * 1000, 10, 'completed'); // Keep completed jobs for 24h, max 10
        const oldFailed = await notificationQueue.clean(7 * 24 * 60 * 60 * 1000, 50, 'failed'); // Keep failed jobs for 7 days, max 50
        
        console.log(`🗑️ Cleaned ${oldCompleted.length} old completed jobs`);
        console.log(`🗑️ Cleaned ${oldFailed.length} old failed jobs`);
        
        console.log("✅ Queue cleanup completed");
        
    } catch (error) {
        console.error("❌ Error clearing stalled jobs:", error);
    } finally {
        await notificationQueue.close();
        process.exit(0);
    }
}

clearStalledJobs();

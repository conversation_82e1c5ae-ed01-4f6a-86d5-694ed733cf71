#!/usr/bin/env node

const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });

const { SerialPort } = require("serialport");
const { ReadlineParser } = require("@serialport/parser-readline");

async function testSMSSequence() {
    const portPath = process.env.MODEM_PORT || "COM8";
    const baudRate = parseInt(process.env.MODEM_BAUD_RATE) || 115200;
    
    console.log(`🧪 Testing SMS Sequence on ${portPath} at ${baudRate} baud`);
    console.log("=".repeat(60));
    
    let port = null;
    let parser = null;
    
    try {
        port = new SerialPort({
            path: portPath,
            baudRate: baudRate,
            dataBits: 8,
            stopBits: 1,
            parity: 'none',
            autoOpen: false,
        });

        parser = port.pipe(new ReadlineParser({ delimiter: "\r\n" }));

        // Open port
        await new Promise((resolve, reject) => {
            port.open((err) => {
                if (err) {
                    reject(err);
                } else {
                    console.log("✅ Port opened");
                    resolve();
                }
            });
        });

        await new Promise((resolve) => {
            port.once("open", resolve);
        });

        console.log("📡 Port ready, testing SMS sequence...\n");

        // Collect all responses
        const allResponses = [];
        parser.on("data", (data) => {
            const cleanData = data.trim();
            if (cleanData) {
                console.log(`📥 Response: "${cleanData}"`);
                allResponses.push(cleanData);
            }
        });

        // Test the exact sequence our app uses
        console.log("1️⃣ Sending ESC to clear any pending operations...");
        port.write(String.fromCharCode(27)); // ESC
        port.write("\r\n");
        await wait(1000);

        console.log("2️⃣ Setting SMS text mode...");
        port.write("AT+CMGF=1\r\n");
        await wait(2000);

        console.log("3️⃣ Setting recipient (this is where the issue occurs)...");
        port.write('AT+CMGS="+967777042721"\r\n');
        await wait(3000); // Wait longer to see what happens

        console.log("4️⃣ Checking if we got SMS prompt (>)...");
        const hasPrompt = allResponses.some(r => r.includes(">"));
        const hasOK = allResponses.some(r => r.includes("OK"));
        
        if (hasPrompt) {
            console.log("✅ Got SMS prompt (>), sending test message...");
            port.write("TEST MESSAGE" + String.fromCharCode(26)); // Ctrl+Z
            await wait(3000);
        } else if (hasOK) {
            console.log("⚠️ Got OK but no prompt, this is the issue!");
            console.log("💡 Some modems need a different approach...");
            
            // Try sending the message anyway
            console.log("5️⃣ Trying to send message without prompt...");
            port.write("TEST MESSAGE" + String.fromCharCode(26)); // Ctrl+Z
            await wait(3000);
        } else {
            console.log("❌ No OK or prompt received");
        }

        console.log("\n📊 Analysis:");
        console.log("=".repeat(30));
        console.log(`Total responses: ${allResponses.length}`);
        console.log("All responses:", allResponses);
        
        const analysis = {
            hasOK: allResponses.some(r => r.includes("OK")),
            hasError: allResponses.some(r => r.includes("ERROR")),
            hasPrompt: allResponses.some(r => r.includes(">")),
            hasCMGS: allResponses.some(r => r.includes("CMGS")),
            hasNumbers: allResponses.some(r => /\d+/.test(r))
        };
        
        console.log("\n🔍 Response Analysis:");
        Object.entries(analysis).forEach(([key, value]) => {
            console.log(`- ${key}: ${value ? '✅' : '❌'}`);
        });
        
        if (analysis.hasOK && !analysis.hasPrompt) {
            console.log("\n💡 Diagnosis: Your modem responds with OK to AT+CMGS but doesn't send the '>' prompt");
            console.log("   This means it might be expecting the message immediately after the command");
            console.log("   or it has a different SMS sending protocol.");
        }
        
        if (analysis.hasCMGS) {
            console.log("\n✅ Good: Modem supports SMS sending (CMGS responses found)");
        }

    } catch (error) {
        console.error("❌ Test failed:", error.message);
    } finally {
        if (port && port.isOpen) {
            console.log("\n🔌 Closing port...");
            port.close();
        }
        process.exit(0);
    }
}

function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

testSMSSequence().catch(console.error);

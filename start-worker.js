#!/usr/bin/env node

const dotenv = require("dotenv");
dotenv.config({ path: "./config.env" });

console.log("🚀 Starting SMS Worker...");

// Import and start the worker
require("./workers/notificationWorker");

// Handle graceful shutdown
process.on('SIGTERM', () => {
    console.log('📴 SMS Worker shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('📴 SMS Worker shutting down gracefully...');
    process.exit(0);
});
